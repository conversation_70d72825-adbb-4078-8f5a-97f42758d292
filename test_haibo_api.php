<?php

/**
 * 海博API测试脚本
 * 
 * 使用方法：
 * php test_haibo_api.php
 */

// 测试配置
$baseUrl = 'http://localhost:8000'; // 根据实际情况修改
$apiUrl = $baseUrl . '/api/haibo/store';

// 测试数据 - 创建门店
$testData = [
    'shopId' => 'HB_TEST_' . time(),
    'shopName' => '海博测试门店',
    'contactPhone' => '13800138000',
    'shopAddress' => '北京市朝阳区广顺北大街666号',
    'shopLng' => 116398419, // 火星坐标，坐标 * 10^6
    'shopLat' => 39908722,  // 火星坐标，坐标 * 10^6
    'carrierMerchantId' => 'CARRIER_' . time(),
    'category' => 1
];

echo "=== 海博API测试 ===\n";
echo "测试URL: $apiUrl\n";
echo "测试数据: " . json_encode($testData, JSON_UNESCAPED_UNICODE) . "\n\n";

// 发送POST请求
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiUrl);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($testData));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/x-www-form-urlencoded',
    'Accept: application/json'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "=== 响应结果 ===\n";
echo "HTTP状态码: $httpCode\n";

if ($error) {
    echo "CURL错误: $error\n";
} else {
    echo "响应内容: $response\n";
    
    $responseData = json_decode($response, true);
    if ($responseData) {
        echo "\n=== 解析后的响应 ===\n";
        echo "Code: " . ($responseData['code'] ?? 'N/A') . "\n";
        echo "Message: " . ($responseData['message'] ?? 'N/A') . "\n";
        
        if (isset($responseData['data'])) {
            echo "Data:\n";
            if (is_array($responseData['data'])) {
                foreach ($responseData['data'] as $key => $value) {
                    echo "  $key: " . (is_array($value) ? json_encode($value) : $value) . "\n";
                }
            } else {
                echo "  " . $responseData['data'] . "\n";
            }
        }
        
        // 判断是否成功
        if (($responseData['code'] ?? -1) === 0) {
            echo "\n✅ 测试成功！\n";
        } else {
            echo "\n❌ 测试失败！\n";
        }
    } else {
        echo "\n❌ 响应格式错误，无法解析JSON\n";
    }
}

echo "\n=== 测试完成 ===\n";

// 测试回调接口
echo "\n=== 测试回调接口 ===\n";

$callbackTests = [
    'ping' => [],
    'balance' => ['shop_id' => $testData['shopId']]
];

foreach ($callbackTests as $command => $data) {
    $callbackUrl = $baseUrl . '/api/haibo/callback/' . $command;
    echo "\n测试回调: $command\n";
    echo "URL: $callbackUrl\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $callbackUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/x-www-form-urlencoded',
        'Accept: application/json'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "HTTP状态码: $httpCode\n";
    echo "响应: $response\n";
}

echo "\n=== 所有测试完成 ===\n";
