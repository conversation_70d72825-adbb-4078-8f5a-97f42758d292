<?php

namespace App\Services;

use App\Models\Common;
use App\Models\Merchant;
use App\Models\MerchantToken;
use App\Models\O2oErrandOrder;
use App\Models\SystemConfig;
use App\Models\User;
use App\Models\UserAddress;
use App\Models\Site;
use App\Models\Pricing;
use App\Services\Amap\GaodeService;
use App\Services\UserService;
use App\Services\MapService;
use App\Services\CommonService;
use App\Services\O2oErrandOrderService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class HaiboService
{
    protected string $appKey;
    protected string $appSecret;

    // 海博订单来源Code
    const TRADE_ORDER_SOURCE_JINGDONG = 300;      // 京东
    const TRADE_ORDER_SOURCE_MEITUAN = 100;       // 美团
    const TRADE_ORDER_SOURCE_ELEME = 200;         // 饿了么
    const TRADE_ORDER_SOURCE_HAIBO = 400;         // 海博自营
    const TRADE_ORDER_SOURCE_YOUZAN = 600;         // 有赞

    const TRADE_ORDER_SOURCE_DOUYIN = 700;         // 抖音
    const TRADE_ORDER_SOURCE_TAOBAO_MAICAI = 800;         // 淘宝买菜

    // 海博商品经营品类Code
    const CATEGORY_FOOD = 100;                    // 美食
    const CATEGORY_FRESH = 104;                   // 生鲜果蔬
    const CATEGORY_MEDICINE = 108;                // 医药健康
    const CATEGORY_SUPERMARKET = 105;             // 超市百货
    const CATEGORY_FLOWER = 103;                  // 鲜花绿植
    const CATEGORY_CAKE = 102;                    // 烘焙蛋糕
    const CATEGORY_DRINK = 101;                   // 饮品奶茶
    const CATEGORY_OTHER = 999;                  // 其他

    // 海博接口返回结果Code
    const RESULT_SUCCESS = 0;                   // 成功
    const RESULT_PARAM_ERROR = 1001;            // 参数错误
    const RESULT_NO_CAPACITY = 1002;            // 无运力
    const RESULT_SYSTEM_ERROR = 9999;           // 系统错误

    public function __construct()
    {
        // 海博平台的配置信息 - 这些需要根据实际的海博平台配置进行调整
        $this->appKey = "haibo_app_key";
        $this->appSecret = "haibo_app_secret";
    }

    /**
     * 创建或修改配送商门店
     *
     * @param array $data 门店数据
     * @return array 处理结果
     * @throws \Exception
     */
    public function createOrUpdateStore(array $data): array
    {
        Log::channel('haibo')->info('海博创建/修改配送商门店请求', $data);

        try {

            // 开始数据库事务
            DB::beginTransaction();

            // 检查是否已存在该门店
            $existingMerchant = $this->findExistingMerchant($data);

            if ($existingMerchant) {
                // 更新现有门店
                $result = $this->updateExistingStore($existingMerchant, $data);
            } else {
                // 创建新门店
                $result = $this->createNewStore($data);
            }

            DB::commit();

            Log::channel('haibo')->info('海博门店操作成功', [
                'operation' => $existingMerchant ? 'update' : 'create',
                'merchant_id' => $result['merchant_id'],
                'user_id' => $result['user_id']
            ]);

            return [
                'success' => true,
                'message' => $existingMerchant ? '门店更新成功' : '门店创建成功',
                'data' => $result
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::channel('haibo')->error('海博门店操作失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);

            throw $e;
        }
    }


    /**
     * 查找已存在的商家
     *
     * @param array $data
     * @return Merchant|null
     */
    private function findExistingMerchant(array $data): ?Merchant
    {
        return Merchant::where('merchant_type', 'haibo')
            ->where('phone', $data['contactPhone'])
            ->first();
    }

    /**
     * 创建新门店
     *
     * @param array $data
     * @return array
     * @throws \Exception
     */
    private function createNewStore(array $data): array
    {
        // 检查该手机号是否已有用户账号
        $user = User::where('phone', $data['contactPhone'])->first();

        // 如果没有用户账号，则创建一个
        if (!$user) {
            $userService = app(UserService::class);
            $user = $userService->registerUser(
                $data['contactPhone'],
                Hash::make('123456'), // 默认密码
                '', // 空邀请码
                \App\Models\SystemConfig::PlatformPT // 平台标识为跑腿平台
            );

            Log::channel('haibo')->info("为海博商家创建关联用户账号成功", [
                'user_id' => $user->id,
                'phone' => $data['contactPhone']
            ]);
        }

        // 解析地址信息（从shopAddress中提取省市区信息）
        $addressInfo = $this->parseAddress($data['shopAddress']);

        // 创建商家账户
        $merchant = Merchant::create([
            'shop_name' => $data['shopName'],
            'phone' => $data['contactPhone'],
            'password' => Hash::make('123456'),
            'province' => $addressInfo['province'],
            'city' => $addressInfo['city'],
            'district' => $addressInfo['district'],
            'city_code' => $addressInfo['city_code'],
            'address' => $data['shopAddress'],
            'contact_name' => '',
            'email' => '',
            'merchant_type' => 'haibo',
            'status' => 1, // 海博商家默认审核通过
            'balance' => 0,
            'user_id' => $user->id,
        ]);

        // 生成配送商门店ID
        $carrierShopId = 'HB_' . $merchant->id;

        return [
            'merchant_id' => $merchant->id,
            'user_id' => $user->id,
            'carrier_shop_id' => $carrierShopId,
            'operation' => 'create'
        ];
    }

    /**
     * 更新现有门店
     *
     * @param Merchant $merchant
     * @param array $data
     * @return array
     */
    private function updateExistingStore(Merchant $merchant, array $data): array
    {
        // 解析地址信息
        $addressInfo = $this->parseAddress($data['shopAddress']);

        // 更新商家信息
        $merchant->update([
            'shop_name' => $data['shopName'],
            'province' => $addressInfo['province'],
            'city' => $addressInfo['city'],
            'district' => $addressInfo['district'],
            'city_code' => $addressInfo['city_code'],
            'address' => $data['shopAddress'],
        ]);


        // 生成配送商门店ID
        $carrierShopId = 'HB_' . $merchant->id;

        return [
            'merchant_id' => $merchant->id,
            'user_id' => $merchant->user_id,
            'carrier_shop_id' => $carrierShopId,
            'operation' => 'update'
        ];
    }


    /**
     * 解析地址信息，从详细地址中提取省市区信息
     *
     * @param string $address
     * @return array
     */
    private function parseAddress(string $address): array
    {
        // 简单的地址解析逻辑，实际项目中可能需要更复杂的解析
        // 这里提供一个基础的解析示例

        $province = '';
        $city = '';
        $district = '';
        $cityCode = '';

        // 常见的省份匹配
        if (preg_match('/(北京|天津|上海|重庆)/', $address, $matches)) {
            $province = $matches[1] . '市';
            $city = $matches[1] . '市';
            $cityCode = $this->getCityCode($province);
        } elseif (preg_match('/(.+?省)(.+?市)(.+?[区县])/', $address, $matches)) {
            $province = $matches[1];
            $city = $matches[2];
            $district = $matches[3];
            $cityCode = $this->getCityCode($city);
        } elseif (preg_match('/(.+?市)(.+?[区县])/', $address, $matches)) {
            // 处理直辖市的情况
            $city = $matches[1];
            $district = $matches[2];
            if (in_array(substr($city, 0, 2), ['北京', '天津', '上海', '重庆'])) {
                $province = $city;
            }
            $cityCode = $this->getCityCode($city);
        }

        // 如果解析失败，使用默认值
        if (empty($province)) {
            $province = '浙江省';
            $city = '杭州市';
            $district = '余杭区';
            $cityCode = '330100';
        }

        return [
            'province' => $province,
            'city' => $city,
            'district' => $district,
            'city_code' => $cityCode
        ];
    }

    /**
     * 获取城市编码（简化版本）
     *
     * @param string $cityName
     * @return string
     */
    private function getCityCode(string $cityName): string
    {
        $cityCodes = [
            '北京市' => '110100',
            '天津市' => '120100',
            '上海市' => '310100',
            '重庆市' => '500100',
            '杭州市' => '330100',
            '广州市' => '440100',
            '深圳市' => '440300',
            '成都市' => '510100',
            '武汉市' => '420100',
            '西安市' => '610100',
        ];

        return $cityCodes[$cityName] ?? '330100'; // 默认杭州
    }


    /**
     * 询价接口 - 按照 maiyatian 模式实现，支持已有订单查询
     *
     * @param array $data 询价请求数据
     * @return array 询价结果
     * @throws \Exception
     */
    public function valuatingWithOrderLookup(array $data): array
    {
        Log::channel('haibo')->info('海博询价请求（新模式）', $data);

        try {
            // 1. 查找用户ID
            $merchant = $this->findByCarrierMerchantId($data['carrierMerchantId']);
            if (!$merchant) {
                return [
                    'code' => self::RESULT_PARAM_ERROR,
                    'message' => '配送商ID不存在',
                    'data' => null
                ];
            }

            $userId = $merchant->user_id;

            // 2. 查找是否已存在订单
            $order = O2oErrandOrder::where('out_order_no', $data['orderId'])
                ->where('app_key', O2oErrandOrder::APP_KEY_HB)
                ->first();

            // 3. 如果订单已存在，返回订单信息
            if ($order) {
                $result = $this->formatExistingOrderResult($order);

                Log::channel('haibo')->info('海博询价成功（已有订单）', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'result' => $result
                ]);

                return [
                    'code' => self::RESULT_SUCCESS,
                    'message' => '成功',
                    'data' => $result
                ];
            }

            // 4. 如果订单不存在，使用 O2oErrandOrderService 进行询价
            $result = $this->performNewOrderValuating($userId, $data);

            Log::channel('haibo')->info('海博询价成功（新订单）', [
                'user_id' => $userId,
                'result' => $result
            ]);

            return [
                'code' => self::RESULT_SUCCESS,
                'message' => '成功',
                'data' => $result
            ];

        } catch (\Exception $e) {
            Log::channel('haibo')->error('海博询价失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);

            return [
                'code' => self::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }





    /**
     * 根据配送商ID查找配送商
     *
     * @param string $carrierMerchantId
     * @return int|null
     */
    private function findByCarrierMerchantId(string $carrierMerchantId)
    {
        $merchant = null;
        if (strpos($carrierMerchantId, 'HB_') === 0) {
            $merchantId = substr($carrierMerchantId, 3);
            $merchant = Merchant::where('merchant_type', 'haibo')
                ->where('id', $merchantId)
                ->first();
        }

        return $merchant;
    }

    /**
     * 格式化已有订单的返回结果
     *
     * @param O2oErrandOrder $order
     * @return array
     */
    private function formatExistingOrderResult(O2oErrandOrder $order): array
    {
        // 计算实际支付金额（与 performNewOrderValuating 逻辑保持一致）
        $actualFee = floatval(fentoyuan($order->actual_amount + $order->gratuity + $order->goods_protected_price));

        return [
            'predictDeliveryTime' => $order->estimated_delivery_time->timestamp,
            'actualFee' => $actualFee,
            'deliveryFee' => $actualFee,
            'deliveryDistance' => $order->distance,
            'discountFee' => 0.0,
            'insuredFee' => 0.0,
        ];
    }

    /**
     * 执行新订单询价
     *
     * @param int $userId
     * @param array $data
     * @return array
     * @throws \Exception
     */
    private function performNewOrderValuating(int $userId, array $data): array
    {
        $service = new O2oErrandOrderService();

        // 转换经纬度格式（海博传入的是整数，需要转换为小数）
        $senderLng = $data['senderLng'] / 1000000;
        $senderLat = $data['senderLat'] / 1000000;
        $recipientLng = $data['recipientLng'] / 1000000;
        $recipientLat = $data['recipientLat'] / 1000000;

        // 准备预约时间
        $appointmentTime = "NOW";
        if ($data['prebook'] == 1) {
            $expectedTime = 0;
            if (isset($data['expectedDeliveryTime']) && $data['expectedDeliveryTime'] > 0) {
                $expectedTime = $data['expectedDeliveryTime'];
            } elseif (isset($data['expectedLeftDeliveryTime']) && $data['expectedLeftDeliveryTime'] > 0) { // 如果预约时间是时间段，则取左区间
                $expectedTime = $data["expectedLeftDeliveryTime"];
            }

            if ($expectedTime > 0) {
                $res = (new GaodeService())->electrobike(strval($senderLng), strval($senderLat), strval($recipientLng), strval($recipientLat));
                $paths = $res["route"]["paths"] ?? [[]];
                $index = 0;
                if (SystemConfig::getCacheConfigValue(SystemConfig::PlatformPT, SystemConfig::PARAM_ORDER_REDUNDANT_TIME) == 2) {
                    $index = count($paths) - 1;
                }
                $needSeconds = $service->getSeconds(intval($paths[$index]["duration"] ?? 0));
                $appointmentTime = date("Y-m-d H:i:s", $expectedTime - $needSeconds) . "|" . date("Y-m-d H:i:s", $expectedTime);
            }
        }

        // 调用 O2oErrandOrderService 的 preOrder 方法
        $preOrderParams = [
            "type" => 1, // 普通配送
            "coupon_id" => 0,
            "gratuity" => 0, // 海博暂不支持小费
            "appointment_time" => $appointmentTime,
            "goods_info" => [
                "is_protect_price" => isset($data['insuredMark']) && $data['insuredMark'] == 1,
                "price" => isset($data['totalValue']) ? $data['totalValue'] : 0,
                "weight" => $data['totalWeight'], // 海博传入的是克，直接使用
            ],
            "start_point" => [
                "mode" => 1, // 指定地址
                "lng" => $senderLng,
                "lat" => $senderLat,
            ],
            "end_point" => [
                "address_id" => 0, // 海博不使用地址簿
                "lng" => $recipientLng,
                "lat" => $recipientLat,
            ],
        ];

        $res = $service->preOrder($userId, $preOrderParams);

        return [
            'predictDeliveryTime' => strtotime($res["normal"]["estimated_delivery_time"]),
            'actualFee' => $res["normal"]["total_amount"],
            'deliveryFee' => $res["normal"]["total_amount"], // 海博的 deliveryFee 等于 actualFee（不含保价费）
            'deliveryDistance' => $res['distance'],
            'discountFee' => 0.00, // 暂不支持优惠
            'insuredFee' => 0.00, // 保价费在 actualFee 中已包含
        ];
    }

    /**
     * 发单接口 - 创建配送订单
     *
     * @param array $data 发单请求数据
     * @return array 发单结果
     * @throws \Exception
     */
    public function send(array $data): array
    {
        Log::channel('haibo')->info('海博发单请求处理开始', $data);

        try {
            // 1. 查找用户ID
            $merchant = $this->findByCarrierMerchantId($data['carrierMerchantId']);
            if (!$merchant) {
                return [
                    'code' => self::RESULT_PARAM_ERROR,
                    'message' => '配送商ID不存在',
                    'data' => null
                ];
            }

            $userId = $merchant->user_id;
            $merchantId = $merchant->id;


            // 3. 准备预约时间
            $appointmentTime = $this->prepareAppointmentTime($data);

            // 4. 准备地址信息
            $addressInfo = $this->prepareAddressInfo($userId, $data);

            // 5. 创建订单
            $order = $this->createOrderWithPreparedData(
                $userId,
                $data,
                $appointmentTime,
                $addressInfo['startAddress'],
                $addressInfo['endAddress'],
                $merchantId
            );

            // 6. 格式化返回结果
            $result = $this->formatOrderResultData($order);

            Log::channel('haibo')->info('海博发单成功', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'result' => $result
            ]);

            return [
                'code' => self::RESULT_SUCCESS,
                'message' => '成功',
                'data' => $result
            ];

        } catch (\Exception $e) {
            Log::channel('haibo')->error('海博发单失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);

            return [
                'code' => self::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 获取商家信息用于订单创建
     *
     * @param array $data
     * @return array
     */
    private function getMerchantInfoForOrder(array $data): array
    {
        $merchant = null;
        if (strpos($data['carrierMerchantId'], 'HB_') === 0) {
            $merchantId = substr($data['carrierMerchantId'], 3);
            $merchant = Merchant::where('merchant_type', 'haibo')
                ->where('id', $merchantId)
                ->first();
        }

        return [
            'merchant_id' => $merchant ? $merchant->id : 0,
            'user_id' => $merchant ? $merchant->user_id : null
        ];
    }

    /**
     * 准备预约时间
     *
     * @param array $data
     * @return string
     */
    private function prepareAppointmentTime(array $data): string
    {
        if ($data['prebook'] == 0) {
            // 即时单
            return "NOW";
        }

        // 预约单 - 需要计算合适的取货时间
        $expectedTime = 0;
        if (isset($data['expectedDeliveryTime']) && $data['expectedDeliveryTime'] > 0) {
            $expectedTime = $data['expectedDeliveryTime'];
        } elseif (isset($data['expectedLeftDeliveryTime']) && $data['expectedLeftDeliveryTime'] > 0) {
            $expectedTime = $data['expectedLeftDeliveryTime'];
        }

        if ($expectedTime > 0) {
            // 使用高德地图API计算配送时间，然后反推取货时间
            try {
                // 转换经纬度格式（海博传入的是整数，需要转换为小数）
                $senderLng = $data['senderLng'] / 1000000;
                $senderLat = $data['senderLat'] / 1000000;
                $recipientLng = $data['recipientLng'] / 1000000;
                $recipientLat = $data['recipientLat'] / 1000000;

                // 调用高德地图API计算路径
                $gaodeService = new GaodeService();
                $res = $gaodeService->electrobike(strval($senderLng),strval($senderLat), strval($recipientLng), strval($recipientLat));

                $paths = $res["route"]["paths"] ?? [[]];
                $index = 0;

                // 根据系统配置选择路径（最快或最慢）
                if (SystemConfig::getCacheConfigValue(SystemConfig::PlatformPT, SystemConfig::PARAM_ORDER_REDUNDANT_TIME) == 2) {
                    $index = count($paths) - 1;
                }

                // 获取配送时长（秒）
                $duration = intval($paths[$index]["duration"] ?? 0);
                $service = new O2oErrandOrderService();
                $needSeconds = $service->getSeconds($duration);

                // 根据期望送达时间反推取货时间
//                $pickupStartTime = $expectedTime - $needSeconds - 30 * 60; // 提前30分钟开始取货
                $pickupStartTime = $expectedTime - $needSeconds; // 提前30分钟开始取货
                $pickupEndTime = $expectedTime;

                $startTime = date("Y-m-d H:i:s", $pickupStartTime);
                $endTime = date("Y-m-d H:i:s", $pickupEndTime);

                return $startTime . "|" . $endTime;

            } catch (\Exception $e) {
                Log::channel('haibo')->warning('计算预约时间失败，使用默认逻辑', [
                    'error' => $e->getMessage(),
                    'data' => $data
                ]);

                // 如果计算失败，使用简单的30分钟提前逻辑
                $startTime = date("Y-m-d H:i:s", $expectedTime - 30 * 60);
                $endTime = date("Y-m-d H:i:s", $expectedTime);
                return $startTime . "|" . $endTime;
            }
        }

        return "NOW";
    }

    /**
     * 准备地址信息
     *
     * @param int $userId
     * @param array $data
     * @return array
     * @throws \Exception
     */
    private function prepareAddressInfo(int $userId, array $data): array
    {
        // 转换经纬度格式（海博传入的是整数，需要转换为小数）
        $senderLng = $data['senderLng'] / 1000000;
        $senderLat = $data['senderLat'] / 1000000;
        $recipientLng = $data['recipientLng'] / 1000000;
        $recipientLat = $data['recipientLat'] / 1000000;

        // 创建发件人地址
        $startAddress = $this->createUserAddress($userId, [
            'name' => $data['senderName'],
            'tel' => $data['senderContract'],
            'address_detail' => $data['senderAddressDetail'],
            'longitude' => $senderLng,
            'latitude' => $senderLat,
            'type' => 'sender'
        ]);

        // 创建收件人地址
        $endAddress = $this->createUserAddress($userId, [
            'name' => $data['recipientName'],
            'tel' => $data['recipientPhone'],
            'address_detail' => $data['recipientAddress'],
            'longitude' => $recipientLng,
            'latitude' => $recipientLat,
            'type' => 'recipient'
        ]);

        return [
            'startAddress' => $startAddress,
            'endAddress' => $endAddress
        ];
    }

    /**
     * 创建用户地址
     *
     * @param int $userId
     * @param array $addressData
     * @return UserAddress
     * @throws \Exception
     */
    private function createUserAddress(int $userId, array $addressData): UserAddress
    {
        // 解析地址信息
        $addressInfo = $this->parseAddress($addressData['address_detail']);

        // 查找已存在的地址
        $address = UserAddress::query()->where("tel", $addressData['tel'])
            ->where("province", $addressInfo['province'])
            ->where("city", $addressInfo['city'])
            ->where("county", $addressInfo['district'])
            ->where("address_detail", $addressData['address_detail'])
            ->where('user_id', $userId)
            ->first();

        if (!$address) {
            $address = UserAddress::create([
                'user_id' => $userId,
                'name' => $addressData['name'],
                'tel' => $addressData['tel'],
                'province' => $addressInfo['province'],
                'city' => $addressInfo['city'],
                'county' => $addressInfo['district'],
                'address_detail' => $addressData['address_detail'],
                'address_remark' => '',
                'longitude' => $addressData['longitude'],
                'latitude' => $addressData['latitude'],
                'is_default' => 0,
            ]);
        }

        return $address;
    }

    /**
     * 使用准备好的数据创建订单
     *
     * @param int $userId
     * @param array $data
     * @param string $appointmentTime
     * @param UserAddress $startAddress
     * @param UserAddress $endAddress
     * @param int $merchantId
     * @return O2oErrandOrder
     * @throws \Exception
     */
    private function createOrderWithPreparedData(
        int $userId,
        array $data,
        string $appointmentTime,
        UserAddress $startAddress,
        UserAddress $endAddress,
        int $merchantId
    ): O2oErrandOrder {
        $service = new O2oErrandOrderService();

        // 商品分类映射表（海博分类到系统分类）
        $goodsCategoryMap = $this->getGoodsCategoryMap();

        // 构建订单标题
        $title = $this->buildOrderTitle($data);

        // 构建订单备注
        $remark = $this->buildOrderRemark($data);

        // 解析商品详情
        $goodsDesc = $this->parseGoodsDetails($data);

        $orderParams = [
            "appointment_time" => $appointmentTime,
            "type" => O2oErrandOrder::TYPE_SEND,
            "coupon_id" => 0,
            "gratuity" => yuantofen($data["tipFee"] ?? 0),
            "out_order_no" => $data["orderId"],
            "app_key" => O2oErrandOrder::APP_KEY_HB,
            'paid_at' => Carbon::now(),
            'pay_method' => Common::PAY_METHOD_OUT_SYSTEM,
            "title" => $title,
            "remark" => $remark,
            "hide_address" => false,
            "is_special" => false,
            "need_incubator" => false,
            "goods_info" => [
                "price" => yuantofen($data["totalValue"] ?? 0),
                "is_protect_price" => isset($data['insuredMark']) && $data['insuredMark'] == 1,
                "desc" => $goodsDesc,
                "imgs" => [],
                "goods_category_id" => $goodsCategoryMap[$data["category"] ?? ""] ?? 14,
                "category_id" => 0,
                "weight" => $data["totalWeight"] ?? 0,
                "volume" => $data["totalVolume"] ?? 0,
            ],
            "start_point" => [
                "mode" => 0,
                "address_id" => $startAddress->id,
                "pickup_code" => false,
                "pickup_code_mode" => 0,
            ],
            "end_point" => [
                "address_id" => $endAddress->id,
                "receive_code" => false,
                "receive_code_mode" => 0,
            ],
        ];

        return $service->createOrder($userId, $orderParams, false, $merchantId);
    }

    /**
     * 获取商品分类映射表
     *
     * @return array
     */
    private function getGoodsCategoryMap(): array
    {
        return [

            // 分类映射成和麦芽田一致
            self::CATEGORY_FOOD => 5,        // 餐饮美食
            self::CATEGORY_FRESH => 7,       // 生鲜果蔬
            self::CATEGORY_MEDICINE => 14,    // 医药健康
            self::CATEGORY_SUPERMARKET => 14, // 超市百货
            self::CATEGORY_FLOWER => 9,      // 鲜花绿植
            self::CATEGORY_CAKE => 8,        // 烘焙蛋糕
            self::CATEGORY_DRINK => 5,       // 饮品奶茶
            self::CATEGORY_OTHER => 14,      // 其他
        ];
    }

    /**
     * 构建订单标题
     *
     * @param array $data
     * @return string
     */
    private function buildOrderTitle(array $data): string
    {
        $sourceMap = [
            self::TRADE_ORDER_SOURCE_JINGDONG => '京东',
            self::TRADE_ORDER_SOURCE_MEITUAN => '美团',
            self::TRADE_ORDER_SOURCE_ELEME => '饿了么',
            self::TRADE_ORDER_SOURCE_HAIBO => '海博',
        ];

        $sourceName = $sourceMap[$data['tradeOrderSource']] ?? '海博';
        $orderSequence = $data['orderSequence'] ?? '';

        if ($orderSequence) {
            return $sourceName . '#' . $orderSequence;
        }

        return $sourceName . '订单';
    }

    /**
     * 构建订单备注
     *
     * @param array $data
     * @return string
     */
    private function buildOrderRemark(array $data): string
    {
        $remarkParts = [];

        if (!empty($data['orderRemark'])) {
            $remarkParts[] = '订单备注：' . $data['orderRemark'];
        }

        if (!empty($data['shopRemark'])) {
            $remarkParts[] = '商户备注：' . $data['shopRemark'];
        }

        if (!empty($data['tradeOrderId'])) {
            $remarkParts[] = '渠道订单号：' . $data['tradeOrderId'];
        }

        return implode(' | ', $remarkParts);
    }

    /**
     * 解析商品详情
     *
     * @param array $data
     * @return string
     */
    private function parseGoodsDetails(array $data): string
    {
        if (empty($data['goodsDetails'])) {
            return '商品';
        }

        try {
            $goodsList = json_decode($data['goodsDetails'], true);
            if (is_array($goodsList) && !empty($goodsList)) {
                $firstGoods = $goodsList[0];
                $name = $firstGoods['name'] ?? '商品';
                $count = count($goodsList);
                return $count > 1 ? $name . '等' . $count . '件商品' : $name;
            }
        } catch (\Exception $e) {
            Log::channel('haibo')->warning('解析商品详情失败', [
                'goodsDetails' => $data['goodsDetails'],
                'error' => $e->getMessage()
            ]);
        }

        return '商品';
    }

    /**
     * 格式化订单结果数据
     *
     * @param O2oErrandOrder $order
     * @return array
     */
    private function formatOrderResultData(O2oErrandOrder $order): array
    {
        // 计算实际支付金额（分转元）
        $actualFee = floatval(fentoyuan($order->actual_amount + $order->gratuity + $order->goods_protected_price));

        // 配送费（不含保价费和小费）
        $deliveryFee = floatval(fentoyuan($order->actual_amount));

        // 小费金额
        $tipFee = floatval(fentoyuan($order->gratuity));

        // 保价费
        $insuredFee = floatval(fentoyuan($order->goods_protected_price));

        return [
            'carrierDeliveryId' => $order->order_no,
            'orderId' => $order->out_order_no,
            'predictDeliveryTime' => $order->estimated_delivery_time->timestamp,
            'actualFee' => $actualFee,
            'discountFee' => 0.0, // 暂不支持优惠
            'insuredFee' => $insuredFee,
            'deliveryFee' => $deliveryFee,
            'tipFee' => $tipFee,
            'deliveryDistance' => $order->distance,
        ];
    }

    /**
     * 取消订单接口 - 参考maiyatian的取消配送实现
     *
     * @param array $data 取消订单请求数据
     * @return array 取消订单结果
     * @throws \Exception
     */
    public function cancelOrder(array $data): array
    {
        Log::channel('haibo')->info('海博取消订单请求处理开始', $data);

        try {
            // 1. 查找订单
            $order = O2oErrandOrder::where('out_order_no', $data['orderId'])
                ->where('app_key', O2oErrandOrder::APP_KEY_HB)
                ->first();

            if (!$order) {
                return [
                    'code' => self::RESULT_PARAM_ERROR,
                    'message' => '订单不存在',
                    'data' => null
                ];
            }

            // 2. 构建取消原因 - 参考maiyatian的实现
            $cancelReason = $this->buildCancelReason($data);

            // 3. 调用 O2oErrandOrderService 的 refundOrder 方法进行退款 - 完全按照maiyatian的方式
            $orderService = new O2oErrandOrderService();
            $refundedOrder = $orderService->refundOrder($order->order_no, $cancelReason);

            $cancelFee = $this->calculateCancelFee($refundedOrder);

            Log::channel('haibo')->info('海博取消订单成功', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'cancel_reason' => $cancelReason,
                'cancel_fee' => $cancelFee
            ]);

            return [
                'code' => self::RESULT_SUCCESS,
                'message' => '成功',
                'data' => [
                    'cancelFee' => $cancelFee
                ]
            ];

        } catch (\Exception $e) {
            Log::channel('haibo')->error('海博取消订单失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);

            return [
                'code' => self::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 构建取消原因 - 参考maiyatian的取消原因映射
     *
     * @param array $data
     * @return string
     */
    private function buildCancelReason(array $data): string
    {
        // 如果有具体的取消原因说明，直接使用
        if (!empty($data['cancelReasonDesc'])) {
            return $data['cancelReasonDesc'];
        }

        // 否则根据取消原因code映射
        $cancelReasonMap = [
            1 => "商户取消",
            2 => "用于用户在渠道或更上一层的取消",
            3 => "用于因为各系统原因发起的取消",
            4 => "配送商使用，用于配送商的骑手发起的取消",
            5 => "配送商使用，用于配送商侧发起的取消配送",
        ];

        return $cancelReasonMap[$data['cancelReasonCode']] ?? "其他原因";
    }

    /**
     * 计算取消扣费（违约金）- 基于实际支付金额和退款金额的差额
     *
     * @param O2oErrandOrder $order
     * @return float
     */
    private function calculateCancelFee(O2oErrandOrder $order): float
    {
        // 计算实际支付金额（分转元）
        $totalPayAmount = floatval(fentoyuan($order->actual_amount + $order->gratuity + $order->goods_protected_price));

        // 计算退款金额（分转元）
        $refundAmount = floatval(fentoyuan($order->refund_amount));

        // 取消扣费 = 实际支付金额 - 退款金额
        $cancelFee = $totalPayAmount - $refundAmount;

        // 确保取消扣费不为负数
        return max(0.0, round($cancelFee, 2));
    }
}
