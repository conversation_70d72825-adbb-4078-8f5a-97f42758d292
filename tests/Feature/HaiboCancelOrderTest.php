<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use App\Models\Merchant;
use App\Models\User;
use App\Models\O2oErrandOrder;
use App\Models\UserAddress;
use Illuminate\Support\Facades\Hash;

class HaiboCancelOrderTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // 创建测试用户和商家
        $this->user = User::factory()->create([
            'phone' => '13800138000',
            'password' => Hash::make('123456'),
        ]);

        $this->merchant = Merchant::create([
            'shop_name' => '测试海博门店',
            'phone' => '13800138000',
            'password' => Hash::make('123456'),
            'province' => '浙江省',
            'city' => '杭州市',
            'district' => '余杭区',
            'city_code' => '330100',
            'address' => '测试地址123号',
            'contact_name' => '张三',
            'email' => '<EMAIL>',
            'merchant_type' => 'haibo',
            'status' => 1,
            'balance' => 0,
            'user_id' => $this->user->id,
        ]);

        // 创建测试地址
        $this->startAddress = UserAddress::create([
            'user_id' => $this->user->id,
            'name' => '发件人',
            'tel' => '13800138001',
            'province' => '浙江省',
            'city' => '杭州市',
            'county' => '余杭区',
            'address_detail' => '发件地址详情',
            'longitude' => 120.123456,
            'latitude' => 30.123456,
            'is_default' => 0,
        ]);

        $this->endAddress = UserAddress::create([
            'user_id' => $this->user->id,
            'name' => '收件人',
            'tel' => '13800138002',
            'province' => '浙江省',
            'city' => '杭州市',
            'county' => '西湖区',
            'address_detail' => '收件地址详情',
            'longitude' => 120.234567,
            'latitude' => 30.234567,
            'is_default' => 0,
        ]);
    }

    /**
     * 测试取消订单 - 订单不存在
     */
    public function test_cancel_order_not_found()
    {
        $cancelData = [
            'orderId' => 'HB_TEST_ORDER_NOT_EXISTS',
            'cancelReasonCode' => 1007,
            'cancelReasonDesc' => '订单取消',
        ];

        $response = $this->postJson('/api/haibo/cancel', $cancelData);

        $response->assertStatus(200)
                ->assertJson([
                    'code' => 1001, // RESULT_PARAM_ERROR
                    'message' => '订单不存在',
                ]);
    }

    /**
     * 测试取消订单 - 参数验证失败
     */
    public function test_cancel_order_validation_fails()
    {
        $invalidData = [
            'orderId' => '', // 空的订单号
            'cancelReasonCode' => 'invalid', // 无效的取消原因code
            'cancelReasonDesc' => '', // 空的取消原因说明
        ];

        $response = $this->postJson('/api/haibo/cancel', $invalidData);

        $response->assertStatus(200)
                ->assertJson([
                    'code' => 1001, // RESULT_PARAM_ERROR
                ]);
    }

    /**
     * 测试取消订单 - 成功取消
     */
    public function test_cancel_order_success()
    {
        // 创建测试订单
        $order = O2oErrandOrder::create([
            'user_id' => $this->user->id,
            'merchant_id' => $this->merchant->id,
            'order_no' => 'TEST_ORDER_' . time(),
            'out_order_no' => 'HB_TEST_ORDER_' . time(),
            'app_key' => O2oErrandOrder::APP_KEY_HB,
            'title' => '测试订单',
            'remark' => '测试备注',
            'order_status' => O2oErrandOrder::STATUS_PAID,
            'refund_status' => O2oErrandOrder::REFUND_STATUS_INIT,
            'actual_amount' => 1000, // 10元
            'gratuity' => 0,
            'goods_protected_price' => 0,
            'distance' => 5000,
            'estimated_delivery_time' => now()->addHour(),
            'start_address_id' => $this->startAddress->id,
            'end_address_id' => $this->endAddress->id,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $cancelData = [
            'orderId' => $order->out_order_no,
            'cancelReasonCode' => 1007,
            'cancelReasonDesc' => '订单取消',
        ];

        $response = $this->postJson('/api/haibo/cancel', $cancelData);

        $response->assertStatus(200)
                ->assertJson([
                    'code' => 0, // RESULT_SUCCESS
                    'message' => '成功',
                ])
                ->assertJsonStructure([
                    'code',
                    'message',
                    'data' => [
                        'cancelFee'
                    ]
                ]);

        // 验证订单状态是否已更新
        $order->refresh();
        $this->assertEquals(O2oErrandOrder::REFUND_STATUS_ING, $order->refund_status);
    }

    /**
     * 测试取消订单 - 订单已退款（由O2oErrandOrderService处理）
     */
    public function test_cancel_order_already_refunded()
    {
        // 创建已退款的测试订单
        $order = O2oErrandOrder::create([
            'user_id' => $this->user->id,
            'merchant_id' => $this->merchant->id,
            'order_no' => 'TEST_ORDER_' . time(),
            'out_order_no' => 'HB_TEST_ORDER_' . time(),
            'app_key' => O2oErrandOrder::APP_KEY_HB,
            'title' => '测试订单',
            'remark' => '测试备注',
            'order_status' => O2oErrandOrder::STATUS_CANCEL,
            'refund_status' => O2oErrandOrder::REFUND_STATUS_SUCCESS, // 已退款
            'actual_amount' => 1000,
            'gratuity' => 0,
            'goods_protected_price' => 0,
            'distance' => 5000,
            'estimated_delivery_time' => now()->addHour(),
            'start_address_id' => $this->startAddress->id,
            'end_address_id' => $this->endAddress->id,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $cancelData = [
            'orderId' => $order->out_order_no,
            'cancelReasonCode' => 1007,
            'cancelReasonDesc' => '订单取消',
        ];

        $response = $this->postJson('/api/haibo/cancel', $cancelData);

        $response->assertStatus(200)
                ->assertJson([
                    'code' => 9999, // RESULT_SYSTEM_ERROR - 因为O2oErrandOrderService会抛出异常
                ]);
    }
}
